name: Build Kernel
on:
  workflow_dispatch:

jobs:
  build:
    name: Build Kernel by ${{ github.actor }}
    runs-on: ubuntu-22.04
    env:
      CCACHE_COMPILERCHECK: "%compiler% -dumpmachine; %compiler% -dumpversion"
      CCACHE_NOHASHDIR: "true"
      CCACHE_HARDLINK: "true"
    steps:
      - uses: actions/checkout@v4
      - name: Prepare Configuration
        run: |
          CONFIG_ENV=$(grep -w "CONFIG_ENV" config.env | head -n 1 | cut -d "=" -f 2)
          CONFIG_LIST=(
              KERNEL_SOURCE
              KERNEL_SOURCE_BRANCH
              KERNEL_CONFIG
              KERNEL_IMAGE_NAME
              ARCH
              ADD_LOCALVERSION_TO_FILENAME
              EXTRA_CMDS
              USE_CUSTOM_CLANG
              CUSTOM_CLANG_SOURCE
              CUSTOM_CLANG_BRANCH
              CUSTOM_CMDS
              CLANG_BRANCH
              CLANG_VERSION
              ENABLE_GCC_ARM64
              ENABLE_GCC_ARM32
              USE_CUSTOM_GCC_64
              CUSTOM_GCC_64_SOURCE
              CUSTOM_GCC_64_BRANCH
              CUSTOM_GCC_64_BIN
              USE_CUSTOM_GCC_32
              CUSTOM_GCC_32_SOURCE
              CUSTOM_GCC_32_BRANCH
              CUSTOM_GCC_32_BIN
              ENABLE_KERNELSU
              KERNELSU_TAG
              ADD_KPROBES_CONFIG
              DISABLE-LTO
              ADD_OVERLAYFS_CONFIG
              DISABLE_CC_WERROR
              APPLY_KSU_PATCH
              USE_CUSTOM_ANYKERNEL3
              CUSTOM_ANYKERNEL3_SOURCE
              CUSTOM_ANYKERNEL3_BRANCH
              ENABLE_CCACHE
              NEED_DTBO
              BUILD_BOOT_IMG
              SOURCE_BOOT_IMAGE
              KSU_EXPECTED_SIZE
              KSU_EXPECTED_HASH
              REMOVE_UNUSED_PACKAGES
          )

          for CONFIG in "${CONFIG_LIST[@]}"; do
              if [[ "$CONFIG" == "EXTRA_CMDS" || "$CONFIG" == "CUSTOM_CMDS" ]]; then
                  echo "$CONFIG=$(grep -w "$CONFIG" "$CONFIG_ENV" | head -n 1 | cut -d ":" -f 2)" >> $GITHUB_ENV
              else
                  echo "$CONFIG=$(grep -w "$CONFIG" "$CONFIG_ENV" | head -n 1 | cut -d "=" -f 2)" >> $GITHUB_ENV
              fi
          done

      - name: Remove unused packages
        if: env.REMOVE_UNUSED_PACKAGES == 'true'
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: true
          android: false
          dotnet: true
          haskell: true
          large-packages: true
          docker-images: true
          swap-storage: false

      - name: Set swap to 10G
        uses: pierotofy/set-swap-space@master
        with:
          swap-size-gb: 10

      - name: Setup build kernel environment
        run: |
          echo "BUILD_TIME=$(TZ=Asia/Shanghai date "+%Y%m%d%H%M")" >> $GITHUB_ENV
          echo "DEVICE=$(echo ${{ env.KERNEL_CONFIG }} | sed 's!vendor/!!;s/_defconfig//;s/_user//;s/-perf//')" >> $GITHUB_ENV
          sudo apt-get update
          sudo -E apt-get -y -qq install git make bc bison ccache openssl dos2unix zip kmod cpio flex libelf-dev curl libssl-dev libtfm-dev wget device-tree-compiler ca-certificates python3 python2 binutils binutils-aarch64-linux-gnu binutils-arm-linux-gnueabi
          mkdir -p $GITHUB_WORKSPACE/kernel_workspace

      - name: Download Clang-aosp
        if: env.USE_CUSTOM_CLANG != 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          mkdir clang-aosp
          wget https://android.googlesource.com/platform/prebuilts/clang/host/linux-x86/+archive/refs/heads/${{ env.CLANG_BRANCH }}/clang-${{ env.CLANG_VERSION }}.tar.gz
          tar -C clang-aosp/ -zxvf clang-${{ env.CLANG_VERSION }}.tar.gz

      - name: Download Custom-Clang
        if: env.USE_CUSTOM_CLANG == 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          if [[ ${{ env.CUSTOM_CLANG_SOURCE }} =~ git ]]; then
              if [[ ${{ env.CUSTOM_CLANG_SOURCE }} == *'.tar.gz' ]]; then
                  wget -O clang.tar.gz ${{ env.CUSTOM_CLANG_SOURCE }}
                  mkdir clang-aosp
                  tar -C clang-aosp/ -zxvf clang.tar.gz
              else
                  git clone ${{ env.CUSTOM_CLANG_SOURCE }} -b ${{ env.CUSTOM_CLANG_BRANCH }} clang-aosp --depth=1
              fi
          else
              wget -O clang.zip ${{ env.CUSTOM_CLANG_SOURCE }}
              mkdir clang-aosp
              unzip clang.zip -d clang-aosp/
          fi

      - name: Download Gcc-aosp
        if: env.USE_CUSTOM_GCC_64 != 'true' || env.USE_CUSTOM_GCC_32 != 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          if [ ${{ env.ENABLE_GCC_ARM64 }} = true ]; then
              mkdir gcc64
              wget -O gcc-aarch64.tar.gz https://android.googlesource.com/platform/prebuilts/gcc/linux-x86/aarch64/aarch64-linux-android-4.9/+archive/refs/tags/android-12.1.0_r27.tar.gz
              tar -C gcc64/ -zxvf gcc-aarch64.tar.gz
              echo "GCC_64=CROSS_COMPILE=$GITHUB_WORKSPACE/kernel_workspace/gcc64/bin/aarch64-linux-android-" >> $GITHUB_ENV
          fi

          if [ ${{ env.ENABLE_GCC_ARM32 }} = true ]; then
              mkdir gcc32
              wget -O gcc-arm.tar.gz https://android.googlesource.com/platform/prebuilts/gcc/linux-x86/arm/arm-linux-androideabi-4.9/+archive/refs/tags/android-12.1.0_r27.tar.gz
              tar -C gcc32/ -zxvf gcc-arm.tar.gz
              echo "GCC_32=CROSS_COMPILE_ARM32=$GITHUB_WORKSPACE/kernel_workspace/gcc32/bin/arm-linux-androideabi-" >> $GITHUB_ENV
          fi

      - name: Download Custom-Gcc
        if: env.USE_CUSTOM_GCC_64 == 'true' || env.USE_CUSTOM_GCC_32 == 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          if [ ${{ env.USE_CUSTOM_GCC_64 }} = true ]; then
            if [[ ${{ env.CUSTOM_GCC_64_SOURCE }} =~ git ]]; then
                if [[ ${{ env.CUSTOM_GCC_64_SOURCE }} == *'.tar.gz' ]]; then
                  wget -O gcc64.tar.gz ${{ env.CUSTOM_GCC_64_SOURCE }}
                  mkdir gcc64
                  tar -C gcc64/ -zxvf gcc64.tar.gz
                else
                  git clone ${{ env.CUSTOM_GCC_64_SOURCE }} -b ${{ env.CUSTOM_GCC_64_BRANCH }} gcc64 --depth=1
                fi
            else
                if [[ ${{ env.CUSTOM_GCC_64_SOURCE }} == *'.tar.gz' ]]; then
                  wget -O gcc64.tar.gz ${{ env.CUSTOM_GCC_64_SOURCE }}
                  mkdir gcc64
                  tar -C gcc64/ -zxvf gcc64.tar.gz
                else
                  wget -O gcc64.zip ${{ env.CUSTOM_GCC_64_SOURCE }}
                  mkdir gcc64
                  unzip gcc64.zip -d gcc64/
                fi
            fi
            GCC64_BIN="${{ env.CUSTOM_GCC_64_BIN }}"
            if [ -z "$GCC64_BIN" ]; then
              GCC64_BIN="aarch64-linux-android-"
            fi
            echo "GCC_64=CROSS_COMPILE=$GITHUB_WORKSPACE/kernel_workspace/gcc64/bin/$GCC64_BIN" >> $GITHUB_ENV
          fi

          if [ ${{ env.USE_CUSTOM_GCC_32 }} = true ]; then
            if [[ ${{ env.CUSTOM_GCC_32_SOURCE }} =~ git ]]; then
                if [[ ${{ env.CUSTOM_GCC_32_SOURCE }} == *'.tar.gz' ]]; then
                  wget -O gcc32.tar.gz ${{ env.CUSTOM_GCC_32_SOURCE }}
                  mkdir gcc32
                  tar -C gcc32/ -zxvf gcc32.tar.gz
                else
                  git clone ${{ env.CUSTOM_GCC_32_SOURCE }} -b ${{ env.CUSTOM_GCC_32_BRANCH }} gcc32 --depth=1
                fi
            else
                if [[ ${{ env.CUSTOM_GCC_32_SOURCE }} == *'.tar.gz' ]]; then
                  wget -O gcc32.tar.gz ${{ env.CUSTOM_GCC_32_SOURCE }}
                  mkdir gcc32
                  tar -C gcc32/ -zxvf gcc32.tar.gz
                else
                  wget -O gcc32.zip ${{ env.CUSTOM_GCC_32_SOURCE }}
                  mkdir gcc32
                  unzip gcc32.zip -d gcc32/
                fi
            fi
            GCC32_BIN="${{ env.CUSTOM_GCC_32_BIN }}"
            if [ -z "$GCC32_BIN" ]; then
              GCC32_BIN="arm-linux-androideabi-"
            fi
            echo "GCC_32=CROSS_COMPILE_ARM32=$GITHUB_WORKSPACE/kernel_workspace/gcc32/bin/$GCC32_BIN" >> $GITHUB_ENV
          fi

      - name: Download mkbootimg tools
        if: env.BUILD_BOOT_IMG == 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          git clone https://android.googlesource.com/platform/system/tools/mkbootimg tools -b master-kernel-build-2022 --depth=1

      - name: Download kernel source
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          git clone --recursive ${{ env.KERNEL_SOURCE }} -b ${{ env.KERNEL_SOURCE_BRANCH }} android-kernel --depth=1
          if [[ ${{ env.ADD_LOCALVERSION_TO_FILENAME }} == 'true' ]]; then
            echo "LOCALVERSION=$(cat android-kernel/localversion)" >> $GITHUB_ENV
          else
            echo "LOCALVERSION=" >> $GITHUB_ENV
          fi

      - name: Download source boot image
        if: env.BUILD_BOOT_IMG == 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          wget -O boot-source.img ${{ env.SOURCE_BOOT_IMAGE }}
          if [ -f boot-source.img ]; then
              echo "FORMAT_MKBOOTING=$(echo `tools/unpack_bootimg.py --boot_img=boot-source.img --format mkbootimg`)" >> $GITHUB_ENV
              echo "HAVE_SOURCE_BOOT_IMAGE=true" >> $GITHUB_ENV
          fi

      - name: Setup KernelSU
        if: env.ENABLE_KERNELSU == 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace/android-kernel
          curl -LSs "https://raw.githubusercontent.com/SukiSU-Ultra/SukiSU-Ultra/main/kernel/setup.sh" | bash -s susfs-1.5.7
          KSU_VERSION=$(cd KernelSU && expr $(/usr/bin/git rev-list --count HEAD) + 10200)

      - name: Setup Configuration for Kernel
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace/android-kernel
          if [ ${{ env.APPLY_KSU_PATCH }} = true ]; then
              bash $GITHUB_WORKSPACE/patches/patches.sh
              if grep -q "CONFIG_KSU" arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}; then
                  sed -i 's/# CONFIG_KSU is not set/CONFIG_KSU=y/g' arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
                  sed -i 's/CONFIG_KSU=n/CONFIG_KSU=y/g' arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
              else
                  echo "CONFIG_KSU=y" >> arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
              fi
          fi
          if [ ${{ env.ADD_KPROBES_CONFIG }} = true -a ${{ env.APPLY_KSU_PATCH }} != true ]; then
              echo "CONFIG_MODULES=y" >> arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
              echo "CONFIG_KPROBES=y" >> arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
              echo "CONFIG_HAVE_KPROBES=y" >> arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
              echo "CONFIG_KPROBE_EVENTS=y" >> arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
          fi
          if [ ${{ env.ADD_OVERLAYFS_CONFIG }} = true ]; then
              echo "CONFIG_OVERLAY_FS=y" >> arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
          fi
          if [ ${{ env.DISABLE-LTO }} = true ]; then
              sed -i 's/CONFIG_LTO=y/CONFIG_LTO=n/' arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
              sed -i 's/CONFIG_LTO_CLANG=y/CONFIG_LTO_CLANG=n/' arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
              sed -i 's/CONFIG_THINLTO=y/CONFIG_THINLTO=n/' arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
              echo "CONFIG_LTO_NONE=y" >> arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
          fi
          if [ ${{ env.DISABLE_CC_WERROR }} = true ]; then
              echo "CONFIG_CC_WERROR=n" >> arch/${{ env.ARCH }}/configs/${{ env.KERNEL_CONFIG }}
          fi

      - name: Setup ccache
        if: env.ENABLE_CCACHE == 'true'
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: build-kernel-${{ env.DEVICE }}${{ env.LOCALVERSION }}${{ env.UPLOADNAME }}
          max-size: 2G

      - name: Build kernel
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace/android-kernel
          export PATH=$GITHUB_WORKSPACE/kernel_workspace/clang-aosp/bin:$GITHUB_WORKSPACE/kernel_workspace/gcc64/bin:$GITHUB_WORKSPACE/kernel_workspace/gcc32/bin:$PATH
          export KBUILD_BUILD_HOST=Github-Action
          export KBUILD_BUILD_USER=$(echo ${{ github.actor }} | tr A-Z a-z)
          if [ ! -z ${{ env.KSU_EXPECTED_SIZE }} ] && [ ! -z ${{ env.KSU_EXPECTED_HASH }} ]; then
            export KSU_EXPECTED_SIZE=${{ env.KSU_EXPECTED_SIZE }}
            export KSU_EXPECTED_HASH=${{ env.KSU_EXPECTED_HASH }}
          fi
          make -s -j$(nproc --all) O=out ARCH=${{ env.ARCH }} ${{ env.KERNEL_CONFIG }}
          if [ ${{ env.ENABLE_CCACHE }} = true ]; then
              make -j$(nproc --all) CC="ccache clang" O=out ARCH=${{ env.ARCH }} ${{ env.CUSTOM_CMDS }} ${{ env.EXTRA_CMDS }} ${{ env.GCC_64 }} ${{ env.GCC_32 }} 2>&1 | tee build.log
          else
              make -j$(nproc --all) CC=clang O=out ARCH=${{ env.ARCH }} ${{ env.CUSTOM_CMDS }} ${{ env.EXTRA_CMDS }} ${{ env.GCC_64 }} ${{ env.GCC_32 }} 2>&1 | tee build.log
          fi
          
          # 从构建日志中获取SukiSU版本、内核版本和类型
          if grep -q "SukiSU version:" build.log; then
              SUKISU_VERSION=$(grep "SukiSU version:" build.log | head -n1 | sed -E 's/.*SukiSU version: ([0-9]+).*/\1/')
              echo "SUKISU_VERSION=$SUKISU_VERSION" >> $GITHUB_ENV
          else
              echo "SUKISU_VERSION=unknown" >> $GITHUB_ENV
          fi
          
          if grep -q "KERNEL_VERSION:" build.log; then
              KERNEL_VERSION=$(grep "KERNEL_VERSION:" build.log | head -n1 | sed -E 's/.*KERNEL_VERSION: ([0-9\.]+).*/\1/')
              echo "KERNEL_VERSION=$KERNEL_VERSION" >> $GITHUB_ENV
          else
              echo "KERNEL_VERSION=unknown" >> $GITHUB_ENV
          fi
          
          if grep -q "KERNEL_TYPE:" build.log; then
              KERNEL_TYPE=$(grep "KERNEL_TYPE:" build.log | head -n1 | sed -E 's/.*KERNEL_TYPE: ([^,]*).*/\1/' | tr -d '\r')
              echo "KERNEL_TYPE=$KERNEL_TYPE" >> $GITHUB_ENV
          else
              echo "KERNEL_TYPE=unknown" >> $GITHUB_ENV
          fi
          
          # 替换KernelSU为SukiSU
          echo "UPLOADNAME=-SukiSU_${SUKISU_VERSION}-${KERNEL_TYPE}-${{ env.KERNEL_VERSION }}" >> $GITHUB_ENV

      - name: Check a kernel output files
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          if [ -f android-kernel/out/arch/${{ env.ARCH }}/boot/${{ env.KERNEL_IMAGE_NAME }} ]; then
              echo "CHECK_FILE_IS_OK=true" >> $GITHUB_ENV
          else
              echo "Kernel output file is empty"
              exit 1
          fi
          if [ ${{ env.NEED_DTBO }} = true ]; then
              if [ -f android-kernel/out/arch/${{ env.ARCH }}/boot/dtbo.img ]; then
                  echo "CHECK_DTBO_IS_OK=true" >> $GITHUB_ENV
              else
                  echo "DTBO image is empty"
                  exit 1
              fi
          fi

      - name: Make Anykernel3
        if: env.CHECK_FILE_IS_OK == 'true' && env.USE_CUSTOM_ANYKERNEL3 != 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          git clone https://github.com/osm0sis/AnyKernel3 --depth=1 AnyKernel3
          sed -i 's/do.devicecheck=1/do.devicecheck=0/g' AnyKernel3/anykernel.sh
          sed -i 's!BLOCK=/dev/block/platform/omap/omap_hsmmc.0/by-name/boot;!BLOCK=auto;!g' AnyKernel3/anykernel.sh
          sed -i 's/IS_SLOT_DEVICE=0;/is_slot_device=auto;/g' AnyKernel3/anykernel.sh
          cp android-kernel/out/arch/${{ env.ARCH }}/boot/${{ env.KERNEL_IMAGE_NAME }} AnyKernel3/
          if [ ${{ env.CHECK_DTBO_IS_OK }} = true ]; then
              cp android-kernel/out/arch/${{ env.ARCH }}/boot/dtbo.img AnyKernel3/
          fi
          rm -rf AnyKernel3/.git* AnyKernel3/README.md

      - name: Make Custom-Anykernel3
        if: env.CHECK_FILE_IS_OK == 'true' && env.USE_CUSTOM_ANYKERNEL3 == 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          if [[ ${{ env.CUSTOM_ANYKERNEL3_SOURCE }} =~ git ]]; then
              if [[ ${{ env.CUSTOM_ANYKERNEL3_SOURCE }} == *'.tar.gz' ]]; then
                  wget -O AnyKernel3.tar.gz ${{ env.CUSTOM_ANYKERNEL3_SOURCE }}
                  mkdir AnyKernel3
                  tar -C AnyKernel3/ -zxvf AnyKernel3.tar.gz
              else
                  git clone ${{ env.CUSTOM_ANYKERNEL3_SOURCE }} -b ${{ env.CUSTOM_ANYKERNEL3_BRANCH }} --depth=1 AnyKernel3
              fi
          else
              wget -O AnyKernel3.zip ${{ env.CUSTOM_ANYKERNEL3_SOURCE }}
              mkdir AnyKernel3
              unzip AnyKernel3.zip -d AnyKernel3/
          fi
          cp android-kernel/out/arch/${{ env.ARCH }}/boot/${{ env.KERNEL_IMAGE_NAME }} AnyKernel3/
          if [ ${{ env.CHECK_DTBO_IS_OK }} = true ]; then
              cp android-kernel/out/arch/${{ env.ARCH }}/boot/dtbo.img AnyKernel3/
          fi
          rm -rf AnyKernel3/.git* AnyKernel3/README.md

      - name: Make boot image
        if: env.HAVE_SOURCE_BOOT_IMAGE == 'true' && env.CHECK_FILE_IS_OK == 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          tools/unpack_bootimg.py --boot_img boot-source.img
          cp android-kernel/out/arch/${{ env.ARCH }}/boot/${{ env.KERNEL_IMAGE_NAME }} out/kernel
          tools/mkbootimg.py ${{ env.FORMAT_MKBOOTING }} -o boot.img
          if [ -f boot.img ]; then
              echo "MAKE_BOOT_IMAGE_IS_OK=true" >> $GITHUB_ENV
          else
              echo "Boot image is empty"
              exit 1
          fi

      - name: Upload ${{ env.KERNEL_IMAGE_NAME }}
        if: env.CHECK_FILE_IS_OK == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.KERNEL_IMAGE_NAME }}${{ env.LOCALVERSION }}${{ env.UPLOADNAME }}-${{ env.DEVICE }}-${{ env.BUILD_TIME }}
          path: kernel_workspace/android-kernel/out/arch/${{ env.ARCH }}/boot/${{ env.KERNEL_IMAGE_NAME }}

      - name: Upload Image
        if: env.CHECK_FILE_IS_OK == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: Image${{ env.LOCALVERSION }}${{ env.UPLOADNAME }}-${{ env.DEVICE }}-${{ env.BUILD_TIME }}
          path: kernel_workspace/android-kernel/out/arch/${{ env.ARCH }}/boot/Image

      - name: Patch and Package KPM Image
        if: env.CHECK_FILE_IS_OK == 'true'
        run: |
          cd $GITHUB_WORKSPACE/kernel_workspace
          mkdir -p kpm-temp
          cp android-kernel/out/arch/${{ env.ARCH }}/boot/Image kpm-temp/
          cp android-kernel/patch_kpm/patch_linux kpm-temp/
          cd kpm-temp
          chmod +x patch_linux
          ./patch_linux
          if [ -f oImage ]; then
            mv oImage Image
            git clone https://github.com/osm0sis/AnyKernel3 --depth=1 AnyKernel3-kpm
            cd AnyKernel3-kpm
            rm -rf .git* README.md
            sed -i 's/do.devicecheck=1/do.devicecheck=0/g' anykernel.sh
            sed -i 's!BLOCK=/dev/block/platform/omap/omap_hsmmc.0/by-name/boot;!BLOCK=auto;!g' anykernel.sh
            sed -i 's/IS_SLOT_DEVICE=0;/is_slot_device=auto;/g' anykernel.sh
            cp ../Image ./
            cd ..
            echo "KPM_IMAGE_READY=true" >> $GITHUB_ENV
          else
            echo "Failed to generate KPM Image"
            exit 1
          fi

      - name: Upload KPM AnyKernel3
        if: env.KPM_IMAGE_READY == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: kpm-Image${{ env.LOCALVERSION }}${{ env.UPLOADNAME }}-${{ env.DEVICE }}-${{ env.BUILD_TIME }}
          path: kernel_workspace/kpm-temp/AnyKernel3-kpm/*

      - name: Upload AnyKernel3
        if: env.CHECK_FILE_IS_OK == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: AnyKernel3${{ env.LOCALVERSION }}${{ env.UPLOADNAME }}-${{ env.DEVICE }}-${{ env.BUILD_TIME }}
          path: kernel_workspace/AnyKernel3/*

      - name: Upload DTBO image
        if: env.CHECK_DTBO_IS_OK == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: dtbo${{ env.LOCALVERSION }}${{ env.UPLOADNAME }}-${{ env.DEVICE }}-${{ env.BUILD_TIME }}
          path: kernel_workspace/android-kernel/out/arch/${{ env.ARCH }}/boot/dtbo.img

      - name: Upload boot image
        if: env.MAKE_BOOT_IMAGE_IS_OK == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: boot${{ env.LOCALVERSION }}${{ env.UPLOADNAME }}-${{ env.DEVICE }}-${{ env.BUILD_TIME }}
          path: kernel_workspace/boot.img
