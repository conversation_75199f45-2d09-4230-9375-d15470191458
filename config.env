CONFIG_ENV=config.env

KERNEL_SOURCE=https://github.com/hotianbexuanto/android_kernel_mtk6873
KERNEL_SOURCE_BRANCH=安卓12-SukiS-dev
KERNEL_CONFIG=mtk6873_defconfig
KERNEL_IMAGE_NAME=Image.gz-dtb
ARCH=arm64
ADD_LOCALVERSION_TO_FILENAME=false
EXTRA_CMDS:LD=ld.lld

# Clang
## Custom
USE_CUSTOM_CLANG=true
CUSTOM_CLANG_SOURCE=https://github.com/crdroidandroid/android_prebuilts_clang_host_linux-x86_clang-6443078.git
CUSTOM_CLANG_BRANCH=10.0

### if your set USE CUSTOM CLANG to false than DO NOT CHANGE CUSTOM CMDS
CUSTOM_CMDS:CLANG_TRIPLE=aarch64-linux-gnu- CROSS_COMPILE=aarch64-linux-android- CROSS_COMPILE_ARM32=arm-linux-androideabi-

## AOSP
CLANG_BRANCH=master-kernel-build-2022
CLANG_VERSION=r450784e

# GCC
ENABLE_GCC_ARM64=true
ENABLE_GCC_ARM32=true
USE_CUSTOM_GCC_64=true
CUSTOM_GCC_64_SOURCE=https://android.googlesource.com/platform/prebuilts/gcc/linux-x86/aarch64/aarch64-linux-android-4.9/+archive/refs/tags/android-12.1.0_r27.tar.gz
CUSTOM_GCC_64_BRANCH=
CUSTOM_GCC_64_BIN=aarch64-linux-android-

USE_CUSTOM_GCC_32=true
CUSTOM_GCC_32_SOURCE=https://android.googlesource.com/platform/prebuilts/gcc/linux-x86/arm/arm-linux-androideabi-4.9/+archive/refs/tags/android-12.1.0_r27.tar.gz
CUSTOM_GCC_32_BRANCH=
CUSTOM_GCC_32_BIN=arm-linux-androideabi-

# KernelSU flags
ENABLE_KERNELSU=true
KERNELSU_TAG=v0.9.5
KSU_EXPECTED_SIZE=
KSU_EXPECTED_HASH=

# Configuration
DISABLE-LTO=true
DISABLE_CC_WERROR=true
ADD_KPROBES_CONFIG=true
ADD_OVERLAYFS_CONFIG=true
APPLY_KSU_PATCH=false
REMOVE_UNUSED_PACKAGES=true

# AnyKernel3
## AnyKernel3 Custom
USE_CUSTOM_ANYKERNEL3=false
CUSTOM_ANYKERNEL3_SOURCE=
CUSTOM_ANYKERNEL3_BRANCH=

# Ccache
ENABLE_CCACHE=true

# DTBO image
NEED_DTBO=false

# Build boot images
BUILD_BOOT_IMG=false
SOURCE_BOOT_IMAGE=https://raw.githubusercontent.com/xiaoleGun/KernelSU_action/main/boot/boot.img
